import { useEffect, useCallback } from 'react'
import { message } from 'antd/es'
import { MessageType } from '@src/common/const'
import { ipConnectivity } from '@src/common/utils'
import { log } from '@ht/xlog'
import type { MessageInstance } from 'antd/es/message/interface'

// 统一的useTranslate hook，支持两种使用模式
interface UseTranslateProps {
  // 可选参数，用于UI交互模式
  chatUiRef?: React.RefObject<any>
  messageApi?: MessageInstance
}

interface UseTranslateReturn {
  // API模式返回的contextHolder
  contextHolder?: React.ReactNode
  // UI交互模式返回的handleTranslate函数
  handleTranslate?: (pageTitle: string) => Promise<void>
}

const useTranslate = (props?: UseTranslateProps): UseTranslateReturn => {
  const [internalMessageApi, contextHolder] = message.useMessage()
  const messageApiToUse = props?.messageApi || internalMessageApi

  const getUserId = () => {
    // 由于移除了登录功能，这里返回一个默认用户ID
    return "anonymous_user"
  }

  // 创建会话
  const createConversation = async (sendResponse: any) => {
    try {
      const response = await fetch(
        `http://10.102.92.209:9607/ai/orchestration/session/createSession`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            UserID: getUserId(),
            appId: 'web-assistant'
          }),
        }
      )

      const res = await response.json()
      console.log('创建会话的回调', res);
      if (res.code === '0') {
        const conversationID = res?.resultData?.conversationId

        if (conversationID) {
          sendResponse({
            code: '0',
            conversationID: conversationID,
          })
        } else {
          sendResponse({
            code: '1',
            error: '会话创建失败',
          })
        }
      }


    } catch (error) {
      console.log('创建会话错误:', error)
      sendResponse({
        code: '1',
        error: error.message,
      })
    }
  }

  // 获取当前标签页
  const getCurrentTab = useCallback(async (messageApi: MessageInstance) => {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      return tabs[0]
    } catch (error) {
      console.error('获取当前标签页失败:', error)
      messageApi.error('获取当前标签页失败')
      return null
    }
  }, [])

  // 刷新当前页面
  const refreshCurrentPage = useCallback(async (tabId: number) => {
    try {
      await chrome.tabs.reload(tabId)
    } catch (error) {
      console.error('刷新页面失败:', error)
    }
  }, [])

  // 记录日志
  const reportLog = useCallback((pageTitle: string) => {
    log({
      id: 'button_click',
      page_id: 'quickReplyBtn',
      page_title: pageTitle,
    })
  }, [])

  // 处理翻译请求
  const handleTranslateAPI = async (query: any, conversationID: any, sendResponse: any) => {
    //todo 发起会话
    try {
      const chatResponse = await fetch(
        `http://10.102.92.209:9607/ai/orchestration/api/v1/chat_query`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            UserID: getUserId(),
            query: query,
            AppConversationID: conversationID,
            ResponseMode: 'blocking',
          }),
        }
      )
      const chatRes = await chatResponse.text()
      console.log('翻译响应:', {
        query,
        chatRes
      })

      try {
        // 参考Java代码的处理方式
        const lines = chatRes.split('\n');
        if (lines.length > 1) {
          // 假设第二行包含我们需要的数据
          const dataLine = lines[1];
          // 假设数据以某个前缀开始
          const prefix = 'data:data: ';
          if (dataLine.startsWith(prefix)) {
            const jsonStr = dataLine.substring(prefix.length);
            const jsonObj = JSON.parse(jsonStr);
            if (jsonObj && jsonObj.answer) {
              console.log('jsonStr:', jsonStr, jsonObj.answer);

              sendResponse({
                code: '0',
                result: jsonObj.answer.replace('翻译结果：', '').trim(),
              });
              return;
            }
          }
        }

        // 如果上面的处理失败，尝试使用正则表达式
        const regex = /翻译结果：(.*?)(?=[^\\]")/;
        const match = chatRes.match(regex);
        if (match) {
          const translation = match[1].trim();
          sendResponse({
            code: '0',
            result: translation,
          });
        } else {
          sendResponse({
            code: '1',
            error: '翻译结果解析失败',
          });
        }
      } catch (parseError) {
        console.log('解析翻译结果错误:', parseError);
        sendResponse({
          code: '1',
          error: '翻译结果解析失败: ' + parseError.message,
        });
      }
    } catch (error) {
      console.log('翻译错误:', error)
      sendResponse({
        code: '1',
        error: error.message,
      })
    }
  }

  // UI交互模式的翻译处理函数
  const handleTranslateUI = useCallback(async (pageTitle: string) => {
    if (!props?.chatUiRef || !messageApiToUse) {
      console.error('UI交互模式需要提供chatUiRef和messageApi参数')
      return
    }

    const handleStartTranslate = (currentTab: chrome.tabs.Tab) => {
      reportLog(pageTitle)
      chrome.tabs.sendMessage(currentTab.id!, {
        type: MessageType.START_TRANSLATE,
      })
    }

    const currentTab = await getCurrentTab(messageApiToUse)
    if (!currentTab?.id) {
      messageApiToUse.error('无法获取当前标签页')
      return
    }
    handleStartTranslate(currentTab)
  }, [props?.chatUiRef, messageApiToUse, reportLog, refreshCurrentPage, getCurrentTab])

  useEffect(() => {
    ipConnectivity(messageApiToUse)
    const messageHandler = (message: any, _sender: any, sendResponse: any) => {
      console.log('收到消息:', message);

      if (message.type === MessageType.CREATE_CONVERSATION) {
        createConversation(sendResponse)
      } else if (message.type === MessageType.BATCH_TRANSLATE) {
        const { query, conversationID } = message.data
        handleTranslateAPI(query, conversationID, sendResponse)
      } else if (message.type === MessageType.SINGLE_TRANSLATE) {
        const { query, conversationID } = message.data
        handleTranslateAPI(query, conversationID, sendResponse)
      }
      return true
    }

    chrome.runtime.onMessage.addListener(messageHandler)

    // 清理函数
    return () => {
      chrome.runtime.onMessage.removeListener(messageHandler)
    }
  }, [messageApiToUse])

  // 根据使用模式返回不同的结果
  if (props?.chatUiRef && props?.messageApi) {
    // UI交互模式
    return {
      handleTranslate: handleTranslateUI
    }
  } else {
    // API模式
    return {
      contextHolder
    }
  }
}

// 导出类型和hook
export type { UseTranslateProps, UseTranslateReturn }
export default useTranslate
